# Notes de développement - Explorer Shortcuts

## Procédure pour modifier les raccourcis clavier

### Structure du code
Les raccourcis clavier sont gérés dans plusieurs fichiers :
- `src/pressKey.ts` : Logique principale des raccourcis (fonctions `keyUp`, `keyDown`, `keysToBlock`)
- `src/modal.ts` : Mo<PERSON> d'aide affichant la liste des raccourcis
- `README.md` : Documentation utilisateur

### Raccourcis actuels avec Alt
Les raccourcis suivants nécessitent maintenant la touche Alt :
- `Alt + n` : Nouveau fichier
- `Alt + f` : Nouveau dossier  
- `Alt + r` : Renommer
- `Alt + x` : Couper
- `Alt + c` : Copier
- `Alt + v` : Coller
- `Alt + w` : Nouvelle fenêtre
- `Alt + h` : Aide
- `Alt + o` : Explorer OS

### Raccourcis sans Alt (inchangés)
- `Delete` : Supprimer
- `F2` : <PERSON>mmer (alternative)
- `Esc` : Annuler opérations
- `↑↓←→` : Navigation (pour l'instant, peut être modifié)

### Pour ajouter Alt à un raccourci existant

1. **Dans `src/pressKey.ts`** :
   - Fonction `keyUp` : Ajouter `&& e.altKey` à la condition
   - Exemple : `if (e.key === 'n' && e.altKey)` au lieu de `if (e.key === 'n')`

2. **Dans `src/pressKey.ts`** :
   - Fonction `keysToBlock` : Retirer la touche de la liste si elle n'a plus de raccourci direct
   - Ou modifier la logique pour vérifier Alt dans `keyDown`

3. **Dans `src/modal.ts`** :
   - Mettre à jour le texte d'aide : `"Alt + N: Create a new file"` au lieu de `"N: Create a new file"`

4. **Dans `README.md`** :
   - Mettre à jour la documentation utilisateur

### Exemple concret : Ajouter Alt aux flèches directionnelles

Si vous décidez d'ajouter Alt aux flèches :

1. Dans `keyUp` :
```typescript
if (e.key === 'ArrowUp' && e.altKey) {
    await navigateOverExplorer(this, "up");
}
```

2. Dans `keysToBlock` :
```typescript
// Retirer 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight' de la liste
// OU modifier keyDown pour vérifier Alt
```

3. Dans la modal :
```typescript
"Alt + Up/Down Arrow: Navigate between files and folders",
```

### Notes importantes
- Toujours tester après modification
- Vérifier que les raccourcis n'interfèrent pas avec Obsidian
- La touche Ctrl est utilisée par Quick Preview dans l'explorer
- Alt est un bon choix car moins utilisé par Obsidian dans l'explorer
